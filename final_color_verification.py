#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终颜色系统验证
"""

import sys
import os

# 添加当前目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def final_color_verification():
    """最终颜色系统验证"""
    print("=== 最终颜色系统验证 ===")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtTest import QTest
        from PyQt5.QtCore import Qt
        
        app = QApplication(sys.argv)
        
        from main_window import MainWindow
        
        # 创建主窗口
        window = MainWindow()
        window.show()
        
        print("✓ 主窗口创建成功")
        
        # 等待界面完全加载
        QTest.qWait(1000)
        app.processEvents()
        
        # 验证分类颜色系统
        print("\n=== 验证分类颜色系统 ===")
        
        # 定义颜色验证规则
        color_rules = {
            # 蓝色系 - 核心检测功能
            "蓝色系": {
                "categories": ["基础检查", "装配", "调试", "测试", "标定"],
                "expected_rgb": (30, 144, 255),
                "description": "核心检测功能"
            },
            # 绿色系 - 扩展功能工具
            "绿色系": {
                "categories": ["便捷工具", "信息查询", "流程生成助手", "状态监控系统", "故障诊断系统"],
                "expected_rgb": (40, 167, 69),
                "description": "扩展功能工具"
            },
            # 紫色系 - 管理设置功能
            "紫色系": {
                "categories": ["社区", "日志管理", "主题设置", "首页"],
                "expected_rgb": (138, 43, 226),
                "description": "管理设置功能"
            }
        }
        
        # 验证每个颜色组
        all_correct = True
        for color_name, rule in color_rules.items():
            print(f"\n🎨 {color_name} - {rule['description']}:")
            expected_r, expected_g, expected_b = rule["expected_rgb"]
            
            for category in rule["categories"]:
                # 查找对应的分类项
                item_found = False
                for i in range(window.category_list.count()):
                    item = window.category_list.item(i)
                    if item.text() == category:
                        item_found = True
                        background = item.background()
                        if background.color().alpha() > 0:
                            color = background.color()
                            r, g, b = color.red(), color.green(), color.blue()
                            
                            # 检查颜色是否匹配
                            if r == expected_r and g == expected_g and b == expected_b:
                                print(f"  ✓ {category}: RGB({r}, {g}, {b}) - 正确")
                            else:
                                print(f"  ❌ {category}: RGB({r}, {g}, {b}) - 错误，期望RGB({expected_r}, {expected_g}, {expected_b})")
                                all_correct = False
                        else:
                            print(f"  ❌ {category}: 无背景颜色")
                            all_correct = False
                        break
                
                if not item_found:
                    print(f"  ❌ {category}: 未找到分类项")
                    all_correct = False
        
        # 测试选择效果
        print(f"\n=== 测试选择高亮效果 ===")
        test_selections = [
            ("基础检查", "蓝色系"),
            ("便捷工具", "绿色系"), 
            ("社区", "紫色系")
        ]
        
        for category, color_type in test_selections:
            for i in range(window.category_list.count()):
                item = window.category_list.item(i)
                if item.text() == category:
                    print(f"选择 {category} ({color_type})...")
                    window.category_list.setCurrentItem(item)
                    QTest.qWait(300)
                    app.processEvents()
                    
                    # 检查选中后的颜色变化
                    selected_color = item.background().color()
                    print(f"  选中后: RGB({selected_color.red()}, {selected_color.green()}, {selected_color.blue()})")
                    break
        
        # 最终结果
        print(f"\n=== 验证结果 ===")
        if all_correct:
            print("🎉 颜色系统验证成功！")
            print("✓ 所有分类都有正确的默认颜色")
            print("✓ 三个颜色组区分明显")
            print("✓ 选择高亮效果正常")
            print("\n📋 分类颜色方案:")
            print("🔵 蓝色系 (核心检测): 基础检查、装配、调试、测试、标定")
            print("🟢 绿色系 (扩展工具): 便捷工具、信息查询、流程生成助手、状态监控系统、故障诊断系统")
            print("🟣 紫色系 (管理设置): 社区、日志管理、主题设置、首页")
        else:
            print("❌ 颜色系统验证失败，存在问题")
        
        # 关闭应用
        window.close()
        app.quit()
        
        return all_correct
        
    except Exception as e:
        print(f"验证失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    final_color_verification()
