#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自适应布局功能演示
展示工具箱在不同窗口状态下的自适应行为
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication, QMessageBox
from PyQt5.QtTest import QTest
from PyQt5.QtCore import Qt, QTimer
from main_window import MainWindow

class AdaptiveLayoutDemo:
    """自适应布局演示类"""
    
    def __init__(self):
        self.app = QApplication.instance()
        if self.app is None:
            self.app = QApplication(sys.argv)
        
        self.window = None
        self.demo_step = 0
        self.timer = QTimer()
        self.timer.timeout.connect(self.next_demo_step)
        
    def start_demo(self):
        """开始演示"""
        print("=== 工具箱自适应布局功能演示 ===")
        print("本演示将展示工具箱在不同窗口状态下的自适应行为")
        print("演示将自动进行，请观察界面变化...")
        
        # 创建主窗口
        self.window = MainWindow()
        self.window.show()
        
        # 等待界面初始化
        QTest.qWait(2000)
        self.app.processEvents()
        
        # 开始演示
        self.timer.start(3000)  # 每3秒切换一次
        
        return self.app.exec_()
    
    def next_demo_step(self):
        """执行下一个演示步骤"""
        if not self.window:
            return
            
        self.demo_step += 1
        
        if self.demo_step == 1:
            self.demo_home_page()
        elif self.demo_step == 2:
            self.demo_window_toggle()
        elif self.demo_step == 3:
            self.demo_app_category()
        elif self.demo_step == 4:
            self.demo_window_toggle()
        elif self.demo_step == 5:
            self.demo_file_explorer()
        elif self.demo_step == 6:
            self.demo_window_toggle()
        elif self.demo_step == 7:
            self.demo_final_summary()
        else:
            self.end_demo()
    
    def demo_home_page(self):
        """演示首页自适应"""
        print("\n--- 步骤1: 首页统计卡片自适应 ---")
        print("当前显示首页，观察统计卡片的尺寸")
        
        # 确保在首页
        if hasattr(self.window, 'show_home_page'):
            self.window.show_home_page()
        
        self.print_current_state()
    
    def demo_window_toggle(self):
        """演示窗口状态切换"""
        current_state = "最大化" if self.window.is_maximized else "恢复窗口"
        target_state = "恢复窗口" if self.window.is_maximized else "最大化"
        
        print(f"\n--- 窗口状态切换: {current_state} → {target_state} ---")
        print("观察所有界面元素如何自动调整尺寸...")
        
        # 执行切换
        self.window.toggle_window_size()
        
        # 等待切换完成
        QTest.qWait(1000)
        self.app.processEvents()
        
        self.print_current_state()
    
    def demo_app_category(self):
        """演示应用分类自适应"""
        print("\n--- 步骤3: 应用卡片自适应 ---")
        print("切换到智能检测分类，观察应用卡片的自适应布局")
        
        # 切换到智能检测分类
        if hasattr(self.window, 'category_list'):
            for i in range(self.window.category_list.count()):
                item = self.window.category_list.item(i)
                if item and "智能检测" in item.text():
                    self.window.category_list.setCurrentRow(i)
                    self.window._on_category_clicked(item)
                    break
        
        QTest.qWait(500)
        self.app.processEvents()
        
        self.print_current_state()
        
        # 统计应用卡片
        from main_window import AppFrame
        app_frames = self.window.scroll_content.findChildren(AppFrame)
        print(f"  显示了 {len(app_frames)} 个应用卡片")
        if app_frames:
            card_size = (app_frames[0].size().width(), app_frames[0].size().height())
            print(f"  卡片尺寸: {card_size}")
    
    def demo_file_explorer(self):
        """演示文件浏览器自适应"""
        print("\n--- 步骤5: 文件浏览器自适应 ---")
        print("切换到文件浏览器，观察文件框的自适应布局")
        
        # 切换到文件浏览器
        if hasattr(self.window, 'category_list'):
            for i in range(self.window.category_list.count()):
                item = self.window.category_list.item(i)
                if item and "文件浏览器" in item.text():
                    self.window.category_list.setCurrentRow(i)
                    self.window._on_category_clicked(item)
                    break
        
        QTest.qWait(500)
        self.app.processEvents()
        
        self.print_current_state()
        
        # 统计文件框
        from main_window import FileExplorerFrame
        file_frames = self.window.scroll_content.findChildren(FileExplorerFrame)
        print(f"  显示了 {len(file_frames)} 个文件框")
        if file_frames:
            file_size = (file_frames[0].size().width(), file_frames[0].size().height())
            print(f"  文件框尺寸: {file_size}")
    
    def demo_final_summary(self):
        """最终总结"""
        print("\n--- 步骤7: 演示总结 ---")
        print("自适应布局功能演示完成！")
        print("\n✓ 已验证的自适应功能:")
        print("  • 首页统计卡片自适应尺寸")
        print("  • 应用卡片自适应尺寸和列数")
        print("  • 文件浏览器框自适应尺寸")
        print("  • 窗口状态切换时的实时调整")
        print("  • 所有界面元素的协调自适应")
        
        self.print_current_state()
        
        print("\n演示将在3秒后结束...")
    
    def print_current_state(self):
        """打印当前状态信息"""
        state = "最大化" if self.window.is_maximized else "恢复窗口"
        cols = self.window.max_cols
        card_size = self.window.adaptive_manager.get_card_size()
        spacing = self.window.adaptive_manager.get_spacing()
        
        print(f"  当前窗口状态: {state}")
        print(f"  布局列数: {cols}")
        print(f"  卡片尺寸: {card_size}")
        print(f"  元素间距: {spacing}px")
    
    def end_demo(self):
        """结束演示"""
        self.timer.stop()
        
        # 显示完成消息
        msg = QMessageBox(self.window)
        msg.setWindowTitle("演示完成")
        msg.setText("自适应布局功能演示已完成！\n\n您可以继续手动测试窗口切换功能，\n观察所有界面元素的自适应行为。")
        msg.setStandardButtons(QMessageBox.Ok)
        msg.exec_()
        
        print("\n=== 演示结束 ===")
        print("窗口将保持打开状态，您可以继续手动测试...")

def main():
    """主函数"""
    demo = AdaptiveLayoutDemo()
    return demo.start_demo()

if __name__ == "__main__":
    main()
