#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
终极验证 - 确认所有功能都正常工作
"""

import sys
import os

# 添加当前目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def ultimate_verification():
    """终极验证"""
    print("=== 🎯 终极验证 - 侧边栏分类颜色系统 ===")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtTest import QTest
        from PyQt5.QtCore import Qt
        
        app = QApplication(sys.argv)
        
        from main_window import MainWindow
        
        # 创建主窗口
        window = MainWindow()
        window.show()
        
        print("✅ 主窗口创建成功")
        
        # 等待界面完全加载
        QTest.qWait(2000)
        app.processEvents()
        
        # 验证分类颜色系统
        print("\n🎨 === 验证分类颜色系统 ===")
        
        # 预期的颜色分组
        expected_colors = {
            "蓝色系 (核心检测功能)": {
                "categories": ["基础检查", "装配", "调试", "测试", "标定"],
                "color_check": lambda r, g, b: b > 200 and r < 50 and g > 100,  # 蓝色主导
                "expected_rgb": "RGB(0, 123, 255)"
            },
            "绿色系 (扩展功能工具)": {
                "categories": ["便捷工具", "信息查询", "流程生成助手", "状态监控系统", "故障诊断系统"],
                "color_check": lambda r, g, b: g > 150 and r < 100 and b < 100,  # 绿色主导
                "expected_rgb": "RGB(40, 167, 69)"
            },
            "橙色系 (管理设置功能)": {
                "categories": ["社区", "日志管理", "主题设置", "首页"],
                "color_check": lambda r, g, b: r > 200 and g > 100 and b < 50,  # 橙色主导
                "expected_rgb": "RGB(255, 123, 0)"
            }
        }
        
        all_correct = True
        
        for color_group, info in expected_colors.items():
            print(f"\n🔍 检查 {color_group}:")
            
            for category in info["categories"]:
                # 查找对应的分类项
                item_found = False
                for i in range(window.category_list.count()):
                    item = window.category_list.item(i)
                    if item.text() == category:
                        item_found = True
                        background = item.background()
                        color = background.color()
                        r, g, b, a = color.red(), color.green(), color.blue(), color.alpha()
                        
                        # 检查颜色是否正确
                        if info["color_check"](r, g, b) and a == 255:
                            print(f"  ✅ {category}: RGB({r}, {g}, {b}) - 正确")
                        else:
                            print(f"  ❌ {category}: RGB({r}, {g}, {b}) - 错误，期望{info['expected_rgb']}")
                            all_correct = False
                        break
                
                if not item_found:
                    print(f"  ❌ {category}: 未找到分类项")
                    all_correct = False
        
        # 测试选择效果
        print(f"\n🖱️ === 测试选择高亮效果 ===")
        test_categories = ["基础检查", "便捷工具", "社区"]
        
        for category in test_categories:
            for i in range(window.category_list.count()):
                item = window.category_list.item(i)
                if item.text() == category:
                    print(f"选择 {category}...")
                    window.category_list.setCurrentItem(item)
                    QTest.qWait(500)
                    app.processEvents()
                    
                    # 检查选中后的颜色变化
                    selected_color = item.background().color()
                    r, g, b = selected_color.red(), selected_color.green(), selected_color.blue()
                    print(f"  选中后颜色: RGB({r}, {g}, {b})")
                    break
        
        # 最终结果
        print(f"\n🏆 === 最终验证结果 ===")
        if all_correct:
            print("🎉 恭喜！侧边栏分类颜色系统完美实现！")
            print("\n📋 功能总结:")
            print("✅ 默认就显示分类颜色（不需要点击）")
            print("✅ 三个颜色组区分非常明显:")
            print("   🔵 蓝色系: 基础检查、装配、调试、测试、标定")
            print("   🟢 绿色系: 便捷工具、信息查询、流程生成助手、状态监控系统、故障诊断系统")
            print("   🟠 橙色系: 社区、日志管理、主题设置、首页")
            print("✅ 点击时有明显的高亮效果")
            print("✅ 颜色对比强烈，一眼就能区分功能分组")
            print("\n🚀 用户体验大幅提升！")
        else:
            print("❌ 验证失败，仍有问题需要解决")
        
        # 关闭应用
        window.close()
        app.quit()
        
        return all_correct
        
    except Exception as e:
        print(f"验证失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    ultimate_verification()
