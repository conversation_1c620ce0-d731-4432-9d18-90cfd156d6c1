#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
from PIL import Image, ImageDraw, ImageFont
import math

def create_animated_gif(filename, text, size=(400, 300), duration=100, frames=20):
    """创建简单的动画gif"""
    images = []
    
    # 尝试使用系统字体
    try:
        font = ImageFont.truetype("C:/Windows/Fonts/simhei.ttf", 24)
    except:
        font = ImageFont.load_default()
    
    for frame in range(frames):
        # 创建图片
        img = Image.new('RGB', size, color='lightblue')
        draw = ImageDraw.Draw(img)
        
        # 计算动画效果 - 旋转的圆圈
        angle = (frame / frames) * 2 * math.pi
        center_x, center_y = size[0] // 2, size[1] // 2
        radius = 50
        
        # 绘制旋转的圆圈
        for i in range(8):
            circle_angle = angle + (i * math.pi / 4)
            x = center_x + radius * math.cos(circle_angle)
            y = center_y + radius * math.sin(circle_angle)
            
            # 圆圈大小随角度变化
            circle_radius = 5 + 3 * math.sin(circle_angle * 2)
            
            draw.ellipse([x-circle_radius, y-circle_radius, 
                         x+circle_radius, y+circle_radius], 
                        fill='red')
        
        # 绘制文本
        bbox = draw.textbbox((0, 0), text, font=font)
        text_width = bbox[2] - bbox[0]
        text_height = bbox[3] - bbox[1]
        text_x = (size[0] - text_width) // 2
        text_y = center_y + 80
        
        draw.text((text_x, text_y), text, fill='black', font=font)
        
        # 添加帧编号
        frame_text = f"Frame {frame + 1}/{frames}"
        draw.text((10, 10), frame_text, fill='darkblue', font=font)
        
        images.append(img)
    
    # 保存为gif
    images[0].save(
        filename,
        save_all=True,
        append_images=images[1:],
        duration=duration,
        loop=0  # 无限循环
    )
    print(f"创建动画gif: {filename}")

def main():
    # 确保image目录存在
    image_dir = "../image"
    if not os.path.exists(image_dir):
        os.makedirs(image_dir)
    
    # 创建测试gif动画
    test_gifs = [
        ("step1.gif", "步骤1\n确保调试操作安全规范"),
        ("step2.gif", "步骤2\n摆放角反射器或金属板"),
        ("step3.gif", "步骤3\n正确连接线路与信号传输"),
        ("lidar1.gif", "激光雷达步骤1\n确保调试操作安全规范"),
        ("lidar2.gif", "激光雷达步骤2\n设置假人或雪糕桶作为目标物"),
        ("lidar3.gif", "激光雷达步骤3\n电气连接"),
        ("camera1.gif", "摄像头步骤1\n检查各传感器的物理指示灯"),
        ("camera2.gif", "摄像头步骤2\n测量标定区域并摆放锥桶"),
        ("camera3.gif", "摄像头步骤3\n获取标定点像素坐标"),
    ]
    
    for filename, text in test_gifs:
        filepath = os.path.join(image_dir, filename)
        create_animated_gif(filepath, text)
    
    print("所有测试gif动画创建完成！")

if __name__ == "__main__":
    main()
