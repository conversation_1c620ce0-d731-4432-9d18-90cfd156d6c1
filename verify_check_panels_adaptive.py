#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证基础检查面板自适应功能的脚本
"""

import re
import os

def check_panel_creation_method():
    """检查面板创建方法的修改"""
    
    print("=== 检查面板创建方法 ===")
    
    if not os.path.exists('main_window.py'):
        print("❌ 未找到 main_window.py 文件")
        return False
    
    with open('main_window.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    checks = [
        (r'setObjectName\("check_items_panel"\)', '检查项目面板对象名设置'),
        (r'setObjectName\("check_content_panel"\)', '检查细则面板对象名设置'),
        (r'setObjectName\("check_standards_panel"\)', '相关标准面板对象名设置'),
        (r'panel\.adaptive_manager = self\.adaptive_manager', '自适应管理器设置'),
        (r'self\._apply_check_panels_adaptive_size', '自适应尺寸应用调用'),
    ]
    
    all_passed = True
    
    for pattern, description in checks:
        if re.search(pattern, content):
            print(f"✓ {description}")
        else:
            print(f"❌ {description}")
            all_passed = False
    
    return all_passed

def check_adaptive_size_method():
    """检查自适应尺寸方法"""
    
    print("\n=== 检查自适应尺寸方法 ===")
    
    with open('main_window.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    checks = [
        (r'def _apply_check_panels_adaptive_size\(self', '自适应尺寸应用方法'),
        (r'is_maximized = self\.adaptive_manager\.current_state == "maximized"', '窗口状态判断'),
        (r'check_items_panel\.setMaximumWidth\(600\)', '最大化时检查项目面板最大宽度'),
        (r'check_items_panel\.setMaximumWidth\(450\)', '恢复时检查项目面板最大宽度'),
        (r'check_content_panel\.setMaximumWidth\(850\)', '最大化时检查细则面板最大宽度'),
        (r'check_content_panel\.setMaximumWidth\(600\)', '恢复时检查细则面板最大宽度'),
        (r'check_standards_panel\.setMaximumWidth\(500\)', '最大化时相关标准面板最大宽度'),
        (r'check_standards_panel\.setMaximumWidth\(350\)', '恢复时相关标准面板最大宽度'),
    ]
    
    all_passed = True
    
    for pattern, description in checks:
        if re.search(pattern, content):
            print(f"✓ {description}")
        else:
            print(f"❌ {description}")
            all_passed = False
    
    return all_passed

def check_adaptive_manager_integration():
    """检查自适应管理器集成"""
    
    print("\n=== 检查自适应管理器集成 ===")
    
    with open('main_window.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    checks = [
        (r'def _update_check_panels_adaptive_size\(self\)', '检查面板自适应更新方法'),
        (r'findChildren\(QWidget, "check_items_panel"\)', '查找检查项目面板'),
        (r'findChildren\(QWidget, "check_content_panel"\)', '查找检查细则面板'),
        (r'findChildren\(QWidget, "check_standards_panel"\)', '查找相关标准面板'),
        (r'self\._update_check_panels_adaptive_size\(\)', '自适应管理器调用检查面板更新'),
    ]
    
    all_passed = True
    
    for pattern, description in checks:
        if re.search(pattern, content):
            print(f"✓ {description}")
        else:
            print(f"❌ {description}")
            all_passed = False
    
    return all_passed

def check_size_values():
    """检查尺寸数值设置"""
    
    print("\n=== 检查尺寸数值设置 ===")
    
    with open('main_window.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查恢复窗口尺寸
    restored_checks = [
        (r'setMaximumWidth\(450\)', '恢复窗口检查项目面板最大宽度 450'),
        (r'setMinimumWidth\(180\)', '恢复窗口检查项目面板最小宽度 180'),
        (r'setMaximumWidth\(600\)', '恢复窗口检查细则面板最大宽度 600'),
        (r'setMinimumWidth\(650\)', '恢复窗口检查细则面板最小宽度 650'),
        (r'setMaximumWidth\(350\)', '恢复窗口相关标准面板最大宽度 350'),
        (r'setMinimumWidth\(400\)', '恢复窗口相关标准面板最小宽度 400'),
    ]
    
    # 检查最大化窗口尺寸
    maximized_checks = [
        (r'setMaximumWidth\(600\)', '最大化窗口检查项目面板最大宽度 600'),
        (r'setMinimumWidth\(250\)', '最大化窗口检查项目面板最小宽度 250'),
        (r'setMaximumWidth\(850\)', '最大化窗口检查细则面板最大宽度 850'),
        (r'setMinimumWidth\(900\)', '最大化窗口检查细则面板最小宽度 900'),
        (r'setMaximumWidth\(500\)', '最大化窗口相关标准面板最大宽度 500'),
        (r'setMinimumWidth\(550\)', '最大化窗口相关标准面板最小宽度 550'),
    ]
    
    all_passed = True
    
    print("恢复窗口尺寸:")
    for pattern, description in restored_checks:
        if re.search(pattern, content):
            print(f"  ✓ {description}")
        else:
            print(f"  ❌ {description}")
            all_passed = False
    
    print("最大化窗口尺寸:")
    for pattern, description in maximized_checks:
        if re.search(pattern, content):
            print(f"  ✓ {description}")
        else:
            print(f"  ❌ {description}")
            all_passed = False
    
    return all_passed

def check_original_fixed_sizes_removed():
    """检查原始固定尺寸是否被正确替换"""

    print("\n=== 检查原始固定尺寸替换 ===")

    with open('main_window.py', 'r', encoding='utf-8') as f:
        content = f.read()

    # 检查是否在_create_single_three_column_group方法中直接设置固定尺寸
    # 而不是调用自适应方法
    lines = content.split('\n')
    in_create_method = False
    found_direct_sizing = False
    found_adaptive_call = False

    for line in lines:
        if 'def _create_single_three_column_group(' in line:
            in_create_method = True
        elif line.strip().startswith('def ') and in_create_method:
            # 进入下一个方法，退出当前方法检查
            break
        elif in_create_method:
            # 检查是否有直接的尺寸设置（不在自适应方法内）
            if ('check_items_panel.setMaximumWidth(' in line or
                'check_content_panel.setMaximumWidth(' in line or
                'check_standards_panel.setMaximumWidth(' in line):
                # 如果不是在自适应方法调用中
                if 'self._apply_check_panels_adaptive_size' not in line:
                    found_direct_sizing = True
            # 检查是否调用了自适应方法
            if 'self._apply_check_panels_adaptive_size' in line:
                found_adaptive_call = True

    if found_direct_sizing:
        print("❌ 在面板创建方法中发现直接的尺寸设置")
        return False
    elif found_adaptive_call:
        print("✓ 面板创建方法正确调用自适应尺寸方法")

        # 检查自适应方法中是否有后备的默认尺寸
        if ('# 如果没有自适应管理器，使用默认尺寸' in content and
            'check_items_panel.setMaximumWidth(500)' in content):
            print("✓ 自适应方法包含正确的后备默认尺寸")
        else:
            print("❌ 自适应方法缺少后备默认尺寸")
            return False

        return True
    else:
        print("❌ 面板创建方法未调用自适应尺寸方法")
        return False

def main():
    """主函数"""
    print("开始验证基础检查面板自适应功能...\n")
    
    checks = [
        check_panel_creation_method,
        check_adaptive_size_method,
        check_adaptive_manager_integration,
        check_size_values,
        check_original_fixed_sizes_removed,
    ]
    
    passed = 0
    total = len(checks)
    
    for check in checks:
        if check():
            passed += 1
        print()  # 空行分隔
    
    print("=== 验证结果 ===")
    print(f"通过: {passed}/{total}")
    
    if passed == total:
        print("🎉 基础检查面板自适应功能验证通过！")
        print("✅ 面板对象名正确设置")
        print("✅ 自适应尺寸方法已实现")
        print("✅ 自适应管理器正确集成")
        print("✅ 尺寸数值设置正确")
        print("✅ 原始固定尺寸已移除")
        
        print("\n📏 自适应规则总结:")
        print("恢复窗口:")
        print("  - 检查项目面板: 最大宽度 450px, 最小宽度 180px")
        print("  - 检查细则面板: 最大宽度 600px, 最小宽度 650px")
        print("  - 相关标准面板: 最大宽度 350px, 最小宽度 400px")
        print("最大化窗口:")
        print("  - 检查项目面板: 最大宽度 600px, 最小宽度 250px")
        print("  - 检查细则面板: 最大宽度 850px, 最小宽度 900px")
        print("  - 相关标准面板: 最大宽度 500px, 最小宽度 550px")
    else:
        print("❌ 部分验证失败，需要进一步检查")
    
    return passed == total

if __name__ == '__main__':
    success = main()
    exit(0 if success else 1)
