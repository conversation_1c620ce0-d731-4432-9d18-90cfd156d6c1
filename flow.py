import os
import json
from PyQt5.QtWidgets import (QWidget, QHBoxLayout, QVBoxLayout, QLabel,
                           QPushButton, QFrame, QScrollArea)
from PyQt5.QtCore import Qt, QSize
from PyQt5.QtGui import QPixmap, QMovie

class FlowWidget(QWidget):
    """流程界面组件 - 嵌入在主窗口中的流程展示界面"""

    def __init__(self, parent=None, category=None, device_name=None):
        super().__init__(parent)
        self.parent_window = parent
        self.current_process = None  # 当前选择的流程类型
        self.current_step = 0  # 当前步骤索引
        self.steps_data = []  # 当前流程的步骤数据
        self.step_buttons = []  # 步骤按钮列表
        self.category = category  # 分类名称（如：装配、调试、标定、测试）
        self.device_name = device_name  # 设备名称（如：毫米波雷达、激光雷达、摄像头）

        # 分类到流程后缀的映射
        self.category_mapping = {
            "装配": "装调",
            "调试": "调试",
            "标定": "标定",
            "测试": "测试"
        }

        # 根据分类和设备名称构建目标流程名称
        self.target_process_name = self.build_process_name(category, device_name)

        # 加载流程数据
        self.load_process_data()

        # 设置界面
        self.setup_ui()

        # 设置自适应管理器
        if hasattr(parent, 'adaptive_manager'):
            self.adaptive_manager = parent.adaptive_manager

    def build_process_name(self, category, device_name):
        """根据分类和设备名称构建流程名称"""
        if not category or not device_name:
            return None

        # 特殊处理不同设备的命名规则
        if device_name == "毫米波雷达":
            # 毫米波雷达的命名规则：毫米波雷达 + 后缀
            process_suffix = self.category_mapping.get(category, "")
            if not process_suffix:
                print(f"未知的分类: {category}")
                return None
            process_name = f"{device_name}{process_suffix}"

        elif device_name == "激光雷达":
            # 激光雷达的命名规则：激光雷达 + 标定流程
            if category == "标定":
                process_name = "激光雷达标定流程"
            else:
                # 如果有其他分类，可以在这里扩展
                print(f"激光雷达暂不支持分类: {category}")
                return None

        elif device_name == "摄像头":
            # 摄像头的命名规则：摄像头 + 标定流程
            if category == "标定":
                process_name = "摄像头标定流程"
            else:
                # 如果有其他分类，可以在这里扩展
                print(f"摄像头暂不支持分类: {category}")
                return None
        else:
            print(f"未知的设备: {device_name}")
            return None

        print(f"构建流程名称: 分类={category}, 设备={device_name} → {process_name}")
        return process_name

        # 设置界面
        self.setup_ui()

        # 设置自适应管理器
        if hasattr(parent, 'adaptive_manager'):
            self.adaptive_manager = parent.adaptive_manager

    def load_process_data(self):
        """加载tac.json中的流程数据"""
        try:
            with open('tac.json', 'r', encoding='utf-8') as f:
                data = json.load(f)
                self.process_data = data.get('processes', {})
                print(f"成功加载流程数据，共{len(self.process_data)}个流程:")
                for process_name in self.process_data.keys():
                    print(f"  - {process_name}")
        except Exception as e:
            print(f"加载流程数据失败: {e}")
            self.process_data = {}

    def setup_ui(self):
        """设置用户界面 - 新布局：上半部分全是图片，下半部分左1/4流程图，右3/4内容"""
        # 主布局 - 垂直分割
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(15)

        # 上半部分：图片区域（整个宽度）
        self.setup_image_area(main_layout)

        # 下半部分：水平分割的容器
        bottom_container = QWidget()
        bottom_layout = QHBoxLayout(bottom_container)
        bottom_layout.setContentsMargins(0, 0, 0, 0)
        bottom_layout.setSpacing(15)

        # 左下：流程图区域 (1/4 宽度)
        self.setup_left_panel(bottom_layout)

        # 右下：内容区域 (3/4 宽度)
        self.setup_right_panel(bottom_layout)

        # 设置下半部分的比例
        bottom_layout.setStretchFactor(self.left_panel, 1)  # 1/4
        bottom_layout.setStretchFactor(self.right_panel, 3)  # 3/4

        # 添加下半部分到主布局
        main_layout.addWidget(bottom_container)

        # 设置上下比例 - 上半部分和下半部分各占一半
        main_layout.setStretchFactor(self.image_area, 1)  # 上半部分
        main_layout.setStretchFactor(bottom_container, 1)  # 下半部分

        # 根据传入的参数加载对应流程
        if self.target_process_name and self.target_process_name in self.process_data:
            self.load_process(self.target_process_name)
            print(f"成功加载流程: {self.target_process_name}")
        elif self.process_data:
            # 如果没有指定流程或流程不存在，加载第一个流程
            first_process = list(self.process_data.keys())[0]
            self.load_process(first_process)
            print(f"加载默认流程: {first_process}")
            if self.target_process_name:
                print(f"警告: 未找到目标流程 '{self.target_process_name}'，可用流程: {list(self.process_data.keys())}")
        else:
            print("错误: 没有可用的流程数据")

    def setup_left_panel(self, main_layout):
        """设置左侧流程图面板"""
        self.left_panel = QFrame()
        self.left_panel.setFrameStyle(QFrame.StyledPanel)
        self.left_panel.setStyleSheet("""
            QFrame {
                background-color: rgba(255, 255, 255, 0.1);
                border: 1px solid rgba(255, 255, 255, 0.2);
                border-radius: 8px;
                padding: 10px;
            }
        """)

        # 左侧布局
        left_layout = QVBoxLayout(self.left_panel)
        left_layout.setContentsMargins(10, 10, 10, 10)
        left_layout.setSpacing(10)

        # 流程标题（显示当前流程名称）
        self.process_title = QLabel("流程图")
        self.process_title.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 18px;
                font-weight: bold;
                margin-bottom: 10px;
                text-align: center;
            }
        """)
        self.process_title.setAlignment(Qt.AlignCenter)
        left_layout.addWidget(self.process_title)

        # 流程图容器
        self.flow_container = QWidget()
        self.flow_layout = QVBoxLayout(self.flow_container)
        self.flow_layout.setContentsMargins(0, 0, 0, 0)
        self.flow_layout.setSpacing(5)

        # 添加滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidget(self.flow_container)
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setStyleSheet("""
            QScrollArea {
                border: none;
                background: transparent;
            }
            QScrollBar:vertical {
                width: 8px;
                background: rgba(255, 255, 255, 0.1);
                border-radius: 4px;
            }
            QScrollBar::handle:vertical {
                background: rgba(255, 255, 255, 0.3);
                border-radius: 4px;
                min-height: 20px;
            }
        """)

        left_layout.addWidget(scroll_area)
        main_layout.addWidget(self.left_panel)



    def setup_right_panel(self, main_layout):
        """设置右下内容面板 - 简化背景层次"""
        self.right_panel = QWidget()  # 改为QWidget，移除边框
        self.right_panel.setStyleSheet("""
            QWidget {
                background-color: rgba(255, 255, 255, 0.08);
                border-radius: 8px;
            }
        """)

        # 右侧布局 - 垂直分割
        right_layout = QVBoxLayout(self.right_panel)
        right_layout.setContentsMargins(15, 15, 15, 15)  # 稍微增加边距
        right_layout.setSpacing(15)  # 增加间距

        # 文字和标准区域
        self.setup_content_area(right_layout)

        main_layout.addWidget(self.right_panel)

    def setup_image_area(self, layout):
        """设置图片显示区域 - 现在是整个上半部分"""
        self.image_area = QFrame()
        self.image_area.setFrameStyle(QFrame.StyledPanel)
        self.image_area.setStyleSheet("""
            QFrame {
                background-color: rgba(0, 0, 0, 0.3);
                border: 1px solid rgba(255, 255, 255, 0.1);
                border-radius: 6px;
            }
        """)

        image_layout = QVBoxLayout(self.image_area)
        image_layout.setContentsMargins(15, 15, 15, 15)

        # 图片标签 - 像图片查看器一样显示
        self.image_label = QLabel()
        self.image_label.setAlignment(Qt.AlignCenter)
        self.image_label.setStyleSheet("""
            QLabel {
                color: white;
                background-color: transparent;
                border: none;
            }
        """)
        # 关键：不使用任何缩放，让图片保持原始比例
        self.image_label.setScaledContents(False)
        self.image_label.setMinimumHeight(300)

        # 设置尺寸策略：让标签适应内容，而不是让内容适应标签
        from PyQt5.QtWidgets import QSizePolicy
        self.image_label.setSizePolicy(QSizePolicy.Preferred, QSizePolicy.Preferred)

        # 用于播放gif动画的QMovie对象
        self.movie = None

        image_layout.addWidget(self.image_label)
        layout.addWidget(self.image_area)

    def setup_content_area(self, layout):
        """设置内容区域（文字解释 + 标准）- 简化背景层次"""
        # 直接在右侧面板中添加内容，不再创建额外的容器

        # 文字解释区域 (2/3)
        self.setup_description_area(layout)

        # 标准区域 (1/3)
        self.setup_standards_area(layout)

    def setup_description_area(self, layout):
        """设置文字解释区域 - 简化背景"""
        self.description_area = QWidget()  # 改为QWidget，移除边框
        self.description_area.setStyleSheet("""
            QWidget {
                background-color: transparent;
            }
        """)

        desc_layout = QVBoxLayout(self.description_area)
        desc_layout.setContentsMargins(5, 5, 5, 5)  # 减少边距
        desc_layout.setSpacing(8)

        # 标题
        desc_title = QLabel("操作说明")
        desc_title.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 18px;
                font-weight: bold;
                margin-bottom: 5px;
                background-color: transparent;
            }
        """)
        desc_layout.addWidget(desc_title)

        # 描述内容
        self.description_label = QLabel()
        self.description_label.setWordWrap(True)
        self.description_label.setAlignment(Qt.AlignTop)
        self.description_label.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 16px;
                line-height: 1.5;
                background-color: rgba(255, 255, 255, 0.05);
                border: 1px solid rgba(255, 255, 255, 0.1);
                border-radius: 6px;
                padding: 10px;
            }
        """)

        # 添加滚动区域
        desc_scroll = QScrollArea()
        desc_scroll.setWidget(self.description_label)
        desc_scroll.setWidgetResizable(True)
        desc_scroll.setStyleSheet("""
            QScrollArea {
                border: none;
                background: transparent;
            }
            QScrollBar:vertical {
                width: 6px;
                background: rgba(255, 255, 255, 0.1);
                border-radius: 3px;
            }
            QScrollBar::handle:vertical {
                background: rgba(255, 255, 255, 0.3);
                border-radius: 3px;
            }
        """)

        desc_layout.addWidget(desc_scroll)
        layout.addWidget(self.description_area, 2)  # 设置拉伸因子为2 (2/3)

    def setup_standards_area(self, layout):
        """设置标准区域 - 简化背景"""
        self.standards_area = QWidget()  # 改为QWidget，移除边框
        self.standards_area.setStyleSheet("""
            QWidget {
                background-color: transparent;
            }
        """)

        standards_layout = QVBoxLayout(self.standards_area)
        standards_layout.setContentsMargins(5, 5, 5, 5)  # 减少边距
        standards_layout.setSpacing(8)

        # 标题
        standards_title = QLabel("相关标准")
        standards_title.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 18px;
                font-weight: bold;
                margin-bottom: 5px;
                background-color: transparent;
            }
        """)
        standards_layout.addWidget(standards_title)

        # 标准容器 - 水平布局，增加高度
        self.standards_container = QWidget()
        self.standards_container.setStyleSheet("""
            QWidget {
                background-color: rgba(255, 255, 255, 0.05);
                border: 1px solid rgba(255, 255, 255, 0.1);
                border-radius: 6px;
                padding: 8px;
                min-height: 80px;
            }
        """)
        self.standards_layout = QHBoxLayout(self.standards_container)
        self.standards_layout.setContentsMargins(15, 15, 15, 15)
        self.standards_layout.setSpacing(15)
        self.standards_layout.setAlignment(Qt.AlignLeft)

        # 添加滚动区域
        standards_scroll = QScrollArea()
        standards_scroll.setWidget(self.standards_container)
        standards_scroll.setWidgetResizable(True)
        standards_scroll.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        standards_scroll.setVerticalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        standards_scroll.setStyleSheet("""
            QScrollArea {
                border: none;
                background: transparent;
            }
            QScrollBar:horizontal {
                height: 6px;
                background: rgba(255, 255, 255, 0.1);
                border-radius: 3px;
            }
            QScrollBar::handle:horizontal {
                background: rgba(255, 255, 255, 0.3);
                border-radius: 3px;
            }
        """)

        standards_layout.addWidget(standards_scroll)
        layout.addWidget(self.standards_area, 1)  # 设置拉伸因子为1 (1/3)

    def load_process(self, process_name):
        """加载指定的流程"""
        if process_name not in self.process_data:
            return

        self.current_process = process_name
        self.steps_data = self.process_data[process_name]
        self.current_step = 0

        # 更新流程标题
        if hasattr(self, 'process_title'):
            # 简化流程名称显示
            display_name = process_name.replace("调试与测试操作流程", "").replace("标定流程", "")
            self.process_title.setText(f"{display_name}流程图")

        # 重新生成流程图
        self.create_flow_chart()

        # 更新右侧内容
        self.update_step_content()

    def create_flow_chart(self):
        """创建流程图"""
        # 清空现有的流程图
        self.clear_flow_layout()
        self.step_buttons = []

        # 显示所有步骤，不再限制为3个
        total_steps = len(self.steps_data)

        for i in range(total_steps):
            step_data = self.steps_data[i]

            # 创建步骤按钮
            step_button = self.create_step_button(step_data, i)
            self.step_buttons.append(step_button)
            self.flow_layout.addWidget(step_button)

            # 添加箭头（除了最后一个步骤）
            if i < total_steps - 1:
                arrow = self.create_arrow()
                self.flow_layout.addWidget(arrow)

        # 添加弹性空间，将"进入下一流程"按钮推到底部
        self.flow_layout.addStretch()

        # 添加"进入下一流程"按钮（固定在底部）
        self.create_next_process_button()

        # 更新按钮状态
        self.update_step_buttons()

    def create_next_process_button(self):
        """创建进入下一流程的按钮"""
        # 定义流程切换顺序
        process_sequence = {
            "装配": "调试",
            "调试": "测试",
            "测试": "标定",
            "标定": None  # 标定是最后一个，没有下一个
        }

        # 获取当前分类
        current_category = self.category
        next_category = process_sequence.get(current_category)

        if next_category:
            # 创建进入下一流程的按钮
            self.next_process_button = QPushButton(f"进入{next_category}步骤")
            self.next_process_button.setStyleSheet("""
                QPushButton {
                    background-color: rgba(255, 193, 7, 0.8);
                    color: black;
                    border: 2px solid rgba(255, 193, 7, 1.0);
                    border-radius: 8px;
                    padding: 12px;
                    font-size: 16px;
                    font-weight: bold;
                    text-align: center;
                    margin-top: 10px;
                }
                QPushButton:hover {
                    background-color: rgba(255, 193, 7, 1.0);
                    border: 2px solid rgba(255, 152, 0, 1.0);
                }
                QPushButton:pressed {
                    background-color: rgba(255, 152, 0, 1.0);
                }
            """)

            # 连接点击事件
            self.next_process_button.clicked.connect(
                lambda: self.switch_to_next_process(next_category)
            )

            # 添加到布局
            self.flow_layout.addWidget(self.next_process_button)
            print(f"创建进入下一流程按钮: {current_category} → {next_category}")
        else:
            # 标定流程，不显示按钮
            self.next_process_button = None
            print(f"当前是最后流程({current_category})，不显示下一流程按钮")

    def switch_to_next_process(self, next_category):
        """切换到下一个流程"""
        print(f"准备切换到下一流程: {next_category}")

        # 通过父窗口切换到对应的分类
        if self.parent_window and hasattr(self.parent_window, 'switch_to_category'):
            self.parent_window.switch_to_category(next_category)
        else:
            print("无法找到父窗口或切换方法")

    def create_step_button(self, step_data, step_index):
        """创建步骤按钮"""
        button = QPushButton()
        button.setMinimumHeight(60)
        button.setMaximumHeight(80)

        # 设置按钮文本 - 使用title
        title = step_data.get('title', f'步骤 {step_index + 1}')
        button.setText(title)

        # 设置基础样式
        button.setStyleSheet(self.get_step_button_style(False))

        # 连接点击事件
        button.clicked.connect(lambda _, idx=step_index: self.switch_to_step(idx))

        # QPushButton不支持setWordWrap，通过样式处理长文本
        if len(title) > 10:  # 如果文本较长，手动添加换行
            lines = []
            words = title.split()
            current_line = ""
            for word in words:
                if len(current_line + word) > 8:  # 每行最多8个字符
                    if current_line:
                        lines.append(current_line.strip())
                        current_line = word + " "
                    else:
                        lines.append(word)
                        current_line = ""
                else:
                    current_line += word + " "
            if current_line:
                lines.append(current_line.strip())
            button.setText("\n".join(lines))

        return button

    def get_step_button_style(self, is_active):
        """获取步骤按钮样式"""
        if is_active:
            # 当前步骤 - 填充颜色
            return """
                QPushButton {
                    background-color: rgba(34, 197, 94, 0.8);
                    color: white;
                    border: 2px solid rgba(34, 197, 94, 1.0);
                    border-radius: 8px;
                    padding: 8px;
                    font-size: 14px;
                    font-weight: bold;
                    text-align: center;
                }
                QPushButton:hover {
                    background-color: rgba(34, 197, 94, 1.0);
                    border: 2px solid rgba(22, 163, 74, 1.0);
                }
                QPushButton:pressed {
                    background-color: rgba(22, 163, 74, 1.0);
                }
            """
        else:
            # 非当前步骤
            return """
                QPushButton {
                    background-color: rgba(255, 255, 255, 0.1);
                    color: white;
                    border: 2px solid rgba(255, 255, 255, 0.3);
                    border-radius: 8px;
                    padding: 8px;
                    font-size: 14px;
                    text-align: center;
                }
                QPushButton:hover {
                    background-color: rgba(255, 255, 255, 0.2);
                    border: 2px solid rgba(255, 255, 255, 0.5);
                }
                QPushButton:pressed {
                    background-color: rgba(255, 255, 255, 0.3);
                }
            """

    def create_arrow(self):
        """创建箭头"""
        arrow_label = QLabel("⬇")
        arrow_label.setAlignment(Qt.AlignCenter)
        arrow_label.setStyleSheet("""
            QLabel {
                color: rgba(59, 130, 246, 1.0);
                font-size: 24px;
                font-weight: bold;
                margin: 8px 0;
                background-color: rgba(255, 255, 255, 0.1);
                border-radius: 4px;
                padding: 2px;
            }
        """)
        arrow_label.setFixedHeight(40)
        return arrow_label

    def clear_flow_layout(self):
        """清空流程图布局"""
        while self.flow_layout.count():
            child = self.flow_layout.takeAt(0)
            if child.widget():
                child.widget().deleteLater()

    def switch_to_step(self, step_index):
        """切换到指定步骤"""
        # 允许在所有步骤之间切换
        if 0 <= step_index < len(self.steps_data):
            self.current_step = step_index
            self.update_step_buttons()
            self.update_step_content()

    def update_step_buttons(self):
        """更新步骤按钮状态"""
        for i, button in enumerate(self.step_buttons):
            is_active = (i == self.current_step)
            button.setStyleSheet(self.get_step_button_style(is_active))

    def update_step_content(self):
        """更新右侧步骤内容"""
        if not self.steps_data or self.current_step >= len(self.steps_data):
            return

        step_data = self.steps_data[self.current_step]

        # 延迟更新图片，确保容器尺寸稳定
        from PyQt5.QtCore import QTimer
        QTimer.singleShot(50, lambda: self.update_step_image(step_data))

        # 立即更新描述和标准
        self.update_step_description(step_data)
        self.update_step_standards(step_data)

    def update_step_image(self, step_data):
        """更新步骤图片，支持静态图片和gif动图"""
        image_path = step_data.get('image', '')

        # 停止之前的动画
        if self.movie:
            self.movie.stop()
            self.movie = None

        # 清除之前的图片
        self.image_label.clear()

        # 尝试加载图片
        image_loaded = False
        if image_path and os.path.exists(image_path):
            try:
                # 检查是否为gif文件
                if image_path.lower().endswith('.gif'):
                    # 使用QMovie播放gif动画 - 像图片查看器一样
                    self.movie = QMovie(image_path)
                    if self.movie.isValid():
                        # 先启动动画以获取第一帧
                        self.movie.start()
                        self.movie.stop()

                        # 获取gif的原始尺寸
                        first_frame = self.movie.currentPixmap()
                        if not first_frame.isNull():
                            original_width = first_frame.width()
                            original_height = first_frame.height()

                            # 获取容器的可用空间
                            container_rect = self.image_area.contentsRect()
                            available_width = container_rect.width() - 40
                            available_height = container_rect.height() - 40

                            # 如果容器太小或为0（初始化时），使用合理的默认值
                            if available_width <= 100:  # 提高阈值
                                available_width = 600  # 增大默认值
                            if available_height <= 100:  # 提高阈值
                                available_height = 400  # 增大默认值

                            print(f"gif容器可用空间: {available_width}x{available_height}")

                            # 智能缩放GIF动画
                            new_width, new_height, scale = self._calculate_smart_scale_size(
                                original_width, original_height, available_width, available_height
                            )

                            # 设置GIF缩放尺寸
                            self.movie.setScaledSize(QSize(new_width, new_height))
                            print(f"智能缩放GIF: {image_path} ({original_width}x{original_height} → {new_width}x{new_height}, 比例: {scale:.2f})")

                        # 设置动画到标签并开始播放
                        self.image_label.setMovie(self.movie)
                        self.movie.start()
                        image_loaded = True
                else:
                    # 静态图片处理 - 像图片查看器一样
                    pixmap = QPixmap(image_path)
                    if not pixmap.isNull():
                        # 获取图片原始尺寸
                        original_width = pixmap.width()
                        original_height = pixmap.height()

                        # 获取容器的可用空间
                        container_rect = self.image_area.contentsRect()
                        available_width = container_rect.width() - 40  # 留边距
                        available_height = container_rect.height() - 40  # 留边距

                        # 如果容器太小或为0（初始化时），使用合理的默认值
                        if available_width <= 100:  # 提高阈值
                            available_width = 600  # 增大默认值
                        if available_height <= 100:  # 提高阈值
                            available_height = 400  # 增大默认值

                        print(f"容器可用空间: {available_width}x{available_height}")

                        # 智能缩放：像手机相册一样填充整个区域
                        scaled_pixmap = self._smart_scale_image(pixmap, available_width, available_height)
                        self.image_label.setPixmap(scaled_pixmap)

                        # 输出缩放信息
                        final_width = scaled_pixmap.width()
                        final_height = scaled_pixmap.height()
                        scale_ratio = final_width / original_width
                        print(f"智能缩放图片: {image_path} ({original_width}x{original_height} → {final_width}x{final_height}, 比例: {scale_ratio:.2f})")

                        image_loaded = True
            except Exception as e:
                print(f"加载图片失败: {e}")

        # 如果图片加载失败，显示默认内容
        if not image_loaded:
            self.set_default_image()

    def _calculate_smart_scale_size(self, original_width, original_height, container_width, container_height):
        """
        计算智能缩放尺寸 - 通用方法，用于静态图片和GIF
        返回: (new_width, new_height, scale_ratio)
        """
        # 计算两种缩放比例
        scale_to_fit_width = container_width / original_width    # 适应宽度的比例
        scale_to_fit_height = container_height / original_height  # 适应高度的比例

        # 选择较大的缩放比例，确保填充整个容器（可能会裁剪）
        scale = max(scale_to_fit_width, scale_to_fit_height)

        # 如果图片很小，允许适度放大（最大3倍）
        if scale > 3.0:
            scale = 3.0
        # 如果图片很大，允许缩小（最小0.1倍）
        elif scale < 0.1:
            scale = 0.1

        # 计算缩放后的尺寸
        new_width = int(original_width * scale)
        new_height = int(original_height * scale)

        return new_width, new_height, scale

    def _smart_scale_image(self, pixmap, container_width, container_height):
        """
        智能缩放图片 - 像手机相册一样的缩放效果
        优先填充整个容器，保持图片比例不变形
        """
        original_width = pixmap.width()
        original_height = pixmap.height()

        # 使用通用计算方法
        new_width, new_height, scale = self._calculate_smart_scale_size(
            original_width, original_height, container_width, container_height
        )

        # 执行缩放
        scaled_pixmap = pixmap.scaled(
            new_width,
            new_height,
            Qt.KeepAspectRatio,
            Qt.SmoothTransformation
        )

        return scaled_pixmap

    def set_default_image(self):
        """设置默认图片"""
        # 停止动画
        if self.movie:
            self.movie.stop()
            self.movie = None

        # 清除图片和动画
        self.image_label.clear()
        self.image_label.setMovie(None)

        # 设置默认文本
        self.image_label.setText("暂无图片")
        self.image_label.setStyleSheet("""
            QLabel {
                color: rgba(255, 255, 255, 0.5);
                font-size: 16px;
                background-color: transparent;
                border: 2px dashed rgba(255, 255, 255, 0.3);
                border-radius: 6px;
            }
        """)

    def update_step_description(self, step_data):
        """更新步骤描述"""
        description = step_data.get('description', '暂无描述')
        self.description_label.setText(description)

    def update_step_standards(self, step_data):
        """更新步骤标准"""
        # 清空现有标准
        self.clear_standards_layout()

        references = step_data.get('references', [])

        if references:
            for ref in references:
                ref_name = ref.get('name', '未知标准')
                ref_button = self.create_standard_button(ref_name, ref)
                self.standards_layout.addWidget(ref_button)
        else:
            # 没有标准时显示提示
            no_standards_label = QLabel("暂无相关标准")
            no_standards_label.setStyleSheet("""
                QLabel {
                    color: rgba(255, 255, 255, 0.5);
                    font-size: 16px;
                    padding: 15px;
                }
            """)
            self.standards_layout.addWidget(no_standards_label)

        # 添加弹性空间
        self.standards_layout.addStretch()

    def create_standard_button(self, name, ref_data):
        """创建标准按钮"""
        button = QPushButton(name)
        button.setStyleSheet("""
            QPushButton {
                background-color: rgba(59, 130, 246, 0.8);
                color: white;
                border: none;
                border-radius: 6px;
                padding: 12px 16px;
                font-size: 14px;
                min-width: 100px;
                max-width: 200px;
                min-height: 40px;
            }
            QPushButton:hover {
                background-color: rgba(59, 130, 246, 1.0);
            }
            QPushButton:pressed {
                background-color: rgba(37, 99, 235, 1.0);
            }
        """)

        # 连接点击事件（可以打开标准文档）
        url = ref_data.get('url', '')
        if url:
            button.clicked.connect(lambda: self.open_standard_document(url))

        return button

    def clear_standards_layout(self):
        """清空标准布局"""
        while self.standards_layout.count():
            child = self.standards_layout.takeAt(0)
            if child.widget():
                child.widget().deleteLater()

    def open_standard_document(self, url):
        """打开标准文档"""
        try:
            import webbrowser
            if url.startswith(('http://', 'https://')):
                webbrowser.open(url)
            elif os.path.exists(url):
                os.startfile(url)  # Windows
        except Exception as e:
            print(f"打开文档失败: {e}")

    def resizeEvent(self, event):
        """窗口大小改变事件"""
        super().resizeEvent(event)
        # 对于gif动画和静态图片，我们现在使用原始尺寸或固定最大尺寸
        # 不需要在窗口调整时重新缩放，避免变形

    def update_adaptive_size(self):
        """更新自适应尺寸"""
        if hasattr(self, 'adaptive_manager') and self.adaptive_manager:
            # 根据窗口状态调整组件尺寸
            is_maximized = self.adaptive_manager.current_state == "maximized"

            if is_maximized:
                # 最大化时的尺寸
                self.setMinimumHeight(600)
            else:
                # 恢复窗口时的尺寸
                self.setMinimumHeight(500)

