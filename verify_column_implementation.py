#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证列数实现的脚本
检查代码中是否正确移除了硬编码的列数
"""

import re
import os

def check_hardcoded_columns():
    """检查main_window.py中的硬编码列数"""
    
    print("=== 检查硬编码列数 ===")
    
    with open('main_window.py', 'r', encoding='utf-8') as f:
        content = f.read()
        lines = content.split('\n')
    
    # 检查是否还有硬编码的6作为列数
    hardcoded_patterns = [
        r'max_cols\s*=\s*6',  # max_cols = 6
        r'create_.*\([^,]*,\s*6\s*,',  # 函数调用中的硬编码6
        r'LayoutFactory\.create_.*\([^,]*,\s*6\s*[,)]',  # LayoutFactory调用中的硬编码6
    ]
    
    issues_found = []
    
    for i, line in enumerate(lines, 1):
        for pattern in hardcoded_patterns:
            if re.search(pattern, line):
                # 排除注释行和常量定义
                if not line.strip().startswith('#') and 'DEFAULT_MAX_COLS' not in line and 'COLS_MAXIMIZED' not in line:
                    issues_found.append((i, line.strip(), pattern))
    
    if issues_found:
        print("❌ 发现硬编码列数问题:")
        for line_num, line_content, pattern in issues_found:
            print(f"  行 {line_num}: {line_content}")
            print(f"    匹配模式: {pattern}")
    else:
        print("✓ 未发现硬编码列数问题")
    
    return len(issues_found) == 0

def check_adaptive_usage():
    """检查自适应管理器的使用"""
    
    print("\n=== 检查自适应管理器使用 ===")
    
    with open('main_window.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查关键的自适应使用模式
    patterns_to_check = [
        (r'self\.adaptive_manager\.get_max_cols\(\)', '自适应管理器获取列数'),
        (r'max_cols=None', '布局工厂方法使用None作为默认值'),
        (r'if max_cols is None.*adaptive_manager', '布局工厂检查自适应管理器'),
        (r'update_adaptive_layout', '智能卡片容器自适应更新'),
    ]
    
    found_patterns = []
    
    for pattern, description in patterns_to_check:
        matches = re.findall(pattern, content, re.DOTALL)
        count = len(matches)
        found_patterns.append((description, count))
        print(f"  {description}: {count} 处")
    
    # 检查是否有足够的自适应使用
    total_adaptive_usage = sum(count for _, count in found_patterns)
    
    if total_adaptive_usage >= 8:  # 期望至少8处自适应使用
        print("✓ 自适应管理器使用充分")
        return True
    else:
        print(f"❌ 自适应管理器使用不足 (发现 {total_adaptive_usage} 处，期望至少 8 处)")
        return False

def check_constants():
    """检查常量定义"""
    
    print("\n=== 检查常量定义 ===")
    
    with open('main_window.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查AdaptiveConstants类
    if 'class AdaptiveConstants:' in content:
        print("✓ 找到AdaptiveConstants类")
        
        # 检查列数常量
        if 'COLS_MAXIMIZED = 6' in content:
            print("✓ 找到最大化窗口列数常量 (6)")
        else:
            print("❌ 未找到最大化窗口列数常量")
            return False
            
        if 'COLS_RESTORED = 4' in content:
            print("✓ 找到恢复窗口列数常量 (4)")
        else:
            print("❌ 未找到恢复窗口列数常量")
            return False
            
        return True
    else:
        print("❌ 未找到AdaptiveConstants类")
        return False

def check_layout_factory_methods():
    """检查布局工厂方法"""
    
    print("\n=== 检查布局工厂方法 ===")
    
    with open('main_window.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    methods_to_check = [
        'create_grid_with_apps',
        'create_smart_cards', 
        'create_category_frame',
        'create_file_grid'
    ]
    
    all_good = True
    
    for method in methods_to_check:
        # 检查方法定义是否使用max_cols=None
        pattern = rf'def {method}\([^)]*max_cols=None'
        if re.search(pattern, content):
            print(f"✓ {method} 使用 max_cols=None 默认值")
        else:
            print(f"❌ {method} 未使用 max_cols=None 默认值")
            all_good = False
    
    return all_good

def main():
    """主函数"""
    print("开始验证列数实现...\n")
    
    if not os.path.exists('main_window.py'):
        print("❌ 未找到 main_window.py 文件")
        return False
    
    checks = [
        check_constants,
        check_layout_factory_methods,
        check_adaptive_usage,
        check_hardcoded_columns,
    ]
    
    passed = 0
    total = len(checks)
    
    for check in checks:
        if check():
            passed += 1
        print()  # 空行分隔
    
    print("=== 验证结果 ===")
    print(f"通过: {passed}/{total}")
    
    if passed == total:
        print("🎉 全局列数规则实现验证通过！")
        print("✓ 恢复窗口使用4列")
        print("✓ 最大化窗口使用6列") 
        print("✓ 所有硬编码已移除")
        print("✓ 自适应管理器正确集成")
    else:
        print("❌ 实现验证失败，需要进一步检查")
    
    return passed == total

if __name__ == '__main__':
    success = main()
    exit(0 if success else 1)
