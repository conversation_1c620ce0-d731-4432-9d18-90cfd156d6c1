#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证流程生成助手自适应功能的脚本
"""

import re
import os

def check_chat_robot_widget_adaptive():
    """检查ChatRobotWidget的自适应功能"""
    
    print("=== 检查ChatRobotWidget自适应功能 ===")
    
    if not os.path.exists('chat_robot_widget.py'):
        print("❌ 未找到 chat_robot_widget.py 文件")
        return False
    
    with open('chat_robot_widget.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    checks = [
        (r'self\.adaptive_manager = None', '自适应管理器属性初始化'),
        (r'def update_adaptive_size\(self\):', '自适应尺寸更新方法'),
        (r'self\.flowchart\.setMinimumHeight', '流程图高度自适应'),
        (r'self\.skills_panel\.setMaximumWidth', '技能面板宽度自适应'),
        (r'self\.tools_panel\.setMaximumWidth', '工具面板宽度自适应'),
        (r'self\.cautions_panel\.setMaximumWidth', '注意事项面板宽度自适应'),
    ]
    
    all_passed = True
    
    for pattern, description in checks:
        if re.search(pattern, content):
            print(f"✓ {description}")
        else:
            print(f"❌ {description}")
            all_passed = False
    
    return all_passed

def check_main_window_integration():
    """检查主窗口中的集成"""
    
    print("\n=== 检查主窗口集成 ===")
    
    if not os.path.exists('main_window.py'):
        print("❌ 未找到 main_window.py 文件")
        return False
    
    with open('main_window.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    checks = [
        (r'self\.chat_robot_widget\.adaptive_manager = self\.adaptive_manager', '自适应管理器设置'),
        (r'self\.main_window\.chat_robot_widget\.update_adaptive_size\(\)', '自适应尺寸更新调用'),
        (r'from chat_robot_widget import ChatRobotWidget', 'ChatRobotWidget导入'),
    ]
    
    all_passed = True
    
    for pattern, description in checks:
        if re.search(pattern, content):
            print(f"✓ {description}")
        else:
            print(f"❌ {description}")
            all_passed = False
    
    return all_passed

def check_fixed_size_removal():
    """检查是否移除了固定尺寸设置"""
    
    print("\n=== 检查固定尺寸移除 ===")
    
    with open('chat_robot_widget.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查ProcessFlowAssistant类中的setFixedSize
    if 'setFixedSize(1900, 1000)' in content:
        print("⚠️  ProcessFlowAssistant类仍使用固定尺寸（这是正常的，因为它是独立窗口）")
    
    # 检查ChatRobotWidget类中是否没有setFixedSize
    lines = content.split('\n')
    in_chat_robot_widget = False
    has_fixed_size_in_widget = False
    
    for line in lines:
        if 'class ChatRobotWidget' in line:
            in_chat_robot_widget = True
        elif line.startswith('class ') and 'ChatRobotWidget' not in line:
            in_chat_robot_widget = False
        elif in_chat_robot_widget and 'setFixedSize' in line:
            has_fixed_size_in_widget = True
            break
    
    if not has_fixed_size_in_widget:
        print("✓ ChatRobotWidget类未使用固定尺寸")
        return True
    else:
        print("❌ ChatRobotWidget类仍使用固定尺寸")
        return False

def check_ui_layout_method():
    """检查UI布局方法"""
    
    print("\n=== 检查UI布局方法 ===")
    
    with open('chat_robot_widget.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查_complete_ui_layout方法是否正确重写
    if 'def _complete_ui_layout(self, main_layout, title_area):' in content:
        print("✓ _complete_ui_layout方法存在")
        
        # 检查是否保存了面板引用
        panel_refs = [
            'self.flowchart =',
            'self.skills_panel =',
            'self.tools_panel =',
            'self.cautions_panel =',
            'self.references_panel ='
        ]
        
        all_refs_found = True
        for ref in panel_refs:
            if ref in content:
                print(f"✓ 找到面板引用: {ref}")
            else:
                print(f"❌ 未找到面板引用: {ref}")
                all_refs_found = False
        
        return all_refs_found
    else:
        print("❌ _complete_ui_layout方法未找到")
        return False

def main():
    """主函数"""
    print("开始验证流程生成助手自适应功能...\n")
    
    checks = [
        check_chat_robot_widget_adaptive,
        check_main_window_integration,
        check_fixed_size_removal,
        check_ui_layout_method,
    ]
    
    passed = 0
    total = len(checks)
    
    for check in checks:
        if check():
            passed += 1
        print()  # 空行分隔
    
    print("=== 验证结果 ===")
    print(f"通过: {passed}/{total}")
    
    if passed == total:
        print("🎉 流程生成助手自适应功能验证通过！")
        print("✅ 自适应管理器正确集成")
        print("✅ 自适应尺寸更新方法已实现")
        print("✅ 主窗口正确调用自适应更新")
        print("✅ UI组件引用正确保存")
    else:
        print("❌ 部分验证失败，需要进一步检查")
    
    return passed == total

if __name__ == '__main__':
    success = main()
    exit(0 if success else 1)
