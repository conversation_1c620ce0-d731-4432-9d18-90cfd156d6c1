#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json

def check_apps_config():
    """检查apps_config.json中的分类和应用"""
    try:
        with open('apps_config.json', 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        print("=== Apps Config 分析 ===")
        
        target_categories = ["调试", "标定", "测试"]
        
        for category in target_categories:
            print(f"\n【{category}】分类:")
            if category in data:
                apps = data[category]
                print(f"  应用数量: {len(apps)}")
                
                for i, app in enumerate(apps):
                    name = app.get('name', '未知')
                    path = app.get('path', '未知')
                    print(f"  {i+1}. {name} -> {path}")
                    
                    # 检查是否指向flow.py
                    if 'flow.py' in path:
                        print(f"     ✓ 指向flow.py")
                    else:
                        print(f"     ✗ 不指向flow.py")
            else:
                print(f"  ❌ 分类不存在")
        
        print("\n=== 检查tac.json中的流程 ===")
        
        try:
            with open('tac.json', 'r', encoding='utf-8') as f:
                tac_data = json.load(f)
            
            processes = tac_data.get('processes', {})
            print(f"tac.json中的流程数量: {len(processes)}")
            
            for process_name in processes.keys():
                print(f"  - {process_name}")
                
        except Exception as e:
            print(f"读取tac.json失败: {e}")
            
    except Exception as e:
        print(f"读取apps_config.json失败: {e}")

def test_flow_mapping():
    """测试流程名称映射"""
    print("\n=== 测试流程名称映射 ===")
    
    # 模拟映射逻辑
    category_mapping = {
        "装配": "装调",
        "调试": "调试", 
        "标定": "标定",
        "测试": "测试"
    }
    
    test_cases = [
        ("装配", "毫米波雷达"),
        ("调试", "毫米波雷达"),
        ("标定", "毫米波雷达"),
        ("测试", "毫米波雷达"),
        ("标定", "激光雷达"),
        ("标定", "摄像头"),
    ]
    
    for category, device in test_cases:
        if device == "毫米波雷达":
            process_suffix = category_mapping.get(category, "")
            process_name = f"{device}{process_suffix}"
        elif device == "激光雷达":
            if category == "标定":
                process_name = "激光雷达标定流程"
            else:
                process_name = f"激光雷达{category}流程"
        elif device == "摄像头":
            if category == "标定":
                process_name = "摄像头标定流程"
            else:
                process_name = f"摄像头{category}流程"
        
        print(f"  {category} + {device} → {process_name}")

if __name__ == "__main__":
    check_apps_config()
    test_flow_mapping()
