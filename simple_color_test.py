#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单颜色测试
"""

import sys
import os

# 添加当前目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def simple_color_test():
    """简单颜色测试"""
    print("=== 简单颜色测试 ===")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtTest import QTest
        from PyQt5.QtCore import Qt
        
        app = QApplication(sys.argv)
        
        from main_window import MainWindow
        
        # 创建主窗口
        window = MainWindow()
        window.show()
        
        print("✓ 主窗口创建成功")
        
        # 等待界面完全加载
        QTest.qWait(2000)
        app.processEvents()
        
        print(f"分类列表项目数量: {window.category_list.count()}")
        
        # 检查每个分类的颜色
        print("\n=== 检查分类颜色 ===")
        for i in range(window.category_list.count()):
            item = window.category_list.item(i)
            category_name = item.text()
            
            # 获取背景颜色
            background = item.background()
            color = background.color()
            
            print(f"{category_name}: RGB({color.red()}, {color.green()}, {color.blue()}, alpha={color.alpha()})")
            
            # 检查是否有颜色
            if color.alpha() > 0 and (color.red() > 0 or color.green() > 0 or color.blue() > 0):
                if color.red() > color.green() and color.red() > color.blue():
                    print(f"  -> 橙色系 ✓")
                elif color.green() > color.red() and color.green() > color.blue():
                    print(f"  -> 绿色系 ✓")
                elif color.blue() > color.red() and color.blue() > color.green():
                    print(f"  -> 蓝色系 ✓")
                else:
                    print(f"  -> 其他颜色 ✓")
            else:
                print(f"  -> 无颜色 ❌")
        
        # 关闭应用
        window.close()
        app.quit()
        
        return True
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    simple_color_test()
