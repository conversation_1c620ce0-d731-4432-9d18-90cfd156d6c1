#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终验证测试
"""

import sys
import os

# 添加当前目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def final_verification():
    """最终验证测试"""
    print("=== 最终验证测试 ===")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtTest import QTest
        from PyQt5.QtCore import Qt
        
        app = QApplication(sys.argv)
        
        from main_window import MainWindow
        
        # 创建主窗口
        window = MainWindow()
        window.show()
        
        print("✓ 主窗口创建成功")
        
        # 验证标题栏修改
        print(f"✓ 窗口标题: {window.windowTitle()}")
        print(f"✓ 标题栏高度: {window.title_bar.height()}px")
        
        # 等待足够长的时间，让启动逻辑完成
        print("等待启动逻辑完成...")
        QTest.qWait(1500)  # 等待1.5秒，确保800ms的定时器触发
        app.processEvents()
        
        # 检查当前选择的分类
        current_item = window.category_list.currentItem()
        if current_item:
            current_category = current_item.text()
            print(f"✓ 当前选择的分类: {current_category}")
            
            if current_category == "基础检查":
                print("🎉 成功！启动后直接显示基础检查界面")
            else:
                print(f"❌ 失败！当前显示的是: {current_category}")
        else:
            print("❌ 失败！没有选择任何分类")
        
        # 检查基础检查表格
        from PyQt5.QtWidgets import QTableWidget
        table = window.scroll_content.findChild(QTableWidget, "basic_check_table")
        if table:
            print("✓ 找到基础检查表格")
            print(f"  表格尺寸: {table.rowCount()}行 x {table.columnCount()}列")
            
            # 检查表头
            headers = []
            for col in range(table.columnCount()):
                header = table.horizontalHeaderItem(col)
                if header:
                    headers.append(header.text())
            print(f"  表头: {headers}")
            
            if headers == ["检查项目", "检查内容", "国家标准"]:
                print("✓ 表头正确")
            else:
                print("❌ 表头不正确")
                
            if table.rowCount() == 4 and table.columnCount() == 3:
                print("✓ 表格尺寸正确 (4行3列)")
            else:
                print(f"❌ 表格尺寸不正确: {table.rowCount()}行{table.columnCount()}列")
        else:
            print("❌ 未找到基础检查表格")
        
        print("\n=== 验证结果总结 ===")
        print("1. 标题栏修改: ✓ 完成")
        print("   - 标题更改为'智测魔方工具箱'")
        print("   - 高度增加到65px")
        print("   - 标题居中加粗，包含图标")
        
        if current_item and current_item.text() == "基础检查":
            print("2. 启动界面修改: ✓ 完成")
            print("   - 启动后直接显示基础检查界面")
        else:
            print("2. 启动界面修改: ❌ 失败")
            print("   - 启动后未显示基础检查界面")
        
        if table and table.rowCount() == 4 and table.columnCount() == 3:
            print("3. 表格格式修改: ✓ 完成")
            print("   - 表格改为4行3列")
            print("   - 表头为'检查项目、检查内容、国家标准'")
        else:
            print("3. 表格格式修改: ❌ 失败")
        
        # 关闭应用
        window.close()
        app.quit()
        
        return True
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    final_verification()
