#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试配置加载问题
"""

import sys
import os
import json

# 添加当前目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_direct_config_load():
    """直接测试配置加载"""
    print("=== 直接配置加载测试 ===")
    
    try:
        with open("apps_config.json", 'r', encoding='utf-8') as f:
            config = json.load(f)
            
        basic_check = config.get("基础检查", [])
        print(f"直接加载 - 基础检查项目数量: {len(basic_check)}")
        
        for item in basic_check:
            print(f"  - {item.get('name')}: path={item.get('path')}")
            
        return config
        
    except Exception as e:
        print(f"直接加载失败: {e}")
        return {}

def test_data_utils_validation():
    """测试DataUtils验证"""
    print("\n=== DataUtils验证测试 ===")
    
    try:
        from main_window import DataUtils
        
        # 测试基础检查项目
        test_item = {
            "name": "安全帽",
            "icon": "../image/OTA1.png",
            "path": "basic_check_item",
            "description": "检查安全帽佩戴情况，确保符合安全标准"
        }
        
        is_valid = DataUtils.validate_app_data(test_item)
        print(f"基础检查项目验证结果: {is_valid}")
        
        # 测试没有path的项目
        test_item_no_path = {
            "name": "安全帽",
            "icon": "../image/OTA1.png",
            "description": "检查安全帽佩戴情况，确保符合安全标准"
        }
        
        is_valid_no_path = DataUtils.validate_app_data(test_item_no_path)
        print(f"无path项目验证结果: {is_valid_no_path}")
        
        return True
        
    except Exception as e:
        print(f"DataUtils验证测试失败: {e}")
        return False

def test_performance_utils_filter():
    """测试PerformanceUtils过滤"""
    print("\n=== PerformanceUtils过滤测试 ===")
    
    try:
        from main_window import PerformanceUtils
        
        # 加载原始配置
        config = test_direct_config_load()
        
        # 应用过滤
        filtered_config = PerformanceUtils.filter_apps_efficiently(config)
        
        basic_check_filtered = filtered_config.get("基础检查", [])
        print(f"过滤后 - 基础检查项目数量: {len(basic_check_filtered)}")
        
        for item in basic_check_filtered:
            print(f"  - {item.get('name')}: path={item.get('path')}")
            
        return filtered_config
        
    except Exception as e:
        print(f"PerformanceUtils过滤测试失败: {e}")
        return {}

def test_startup_cache():
    """测试启动缓存"""
    print("\n=== 启动缓存测试 ===")
    
    try:
        import startup_cache
        
        cache_manager = startup_cache.StartupCache()
        config = cache_manager.load_apps_data("apps_config.json")
        
        basic_check_cached = config.get("基础检查", [])
        print(f"缓存加载 - 基础检查项目数量: {len(basic_check_cached)}")
        
        for item in basic_check_cached:
            print(f"  - {item.get('name')}: path={item.get('path')}")
            
        return config
        
    except Exception as e:
        print(f"启动缓存测试失败: {e}")
        return {}

def test_main_window_load_config():
    """测试主窗口配置加载"""
    print("\n=== 主窗口配置加载测试 ===")
    
    try:
        from PyQt5.QtWidgets import QApplication
        app = QApplication(sys.argv)
        
        from main_window import MainWindow
        
        # 创建主窗口但不显示
        window = MainWindow()
        
        print(f"主窗口配置文件: {window.config_file}")
        
        # 测试load_config方法
        config1 = window.load_config()
        basic_check1 = config1.get("基础检查", [])
        print(f"load_config - 基础检查项目数量: {len(basic_check1)}")
        
        # 测试load_config_optimized方法
        config2 = window.load_config_optimized()
        basic_check2 = config2.get("基础检查", [])
        print(f"load_config_optimized - 基础检查项目数量: {len(basic_check2)}")
        
        # 检查实际的apps_data
        basic_check3 = window.apps_data.get("基础检查", [])
        print(f"window.apps_data - 基础检查项目数量: {len(basic_check3)}")
        
        app.quit()
        return True
        
    except Exception as e:
        print(f"主窗口配置加载测试失败: {e}")
        return False

def main():
    """主函数"""
    print("开始调试配置加载问题...\n")
    
    test_direct_config_load()
    test_data_utils_validation()
    test_performance_utils_filter()
    test_startup_cache()
    test_main_window_load_config()
    
    print("\n调试完成!")

if __name__ == "__main__":
    main()
