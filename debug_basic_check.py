#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试基础检查功能
"""

import sys
import os

# 添加当前目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def debug_basic_check():
    """调试基础检查功能"""
    print("=== 调试基础检查功能 ===")
    
    try:
        from PyQt5.QtWidgets import QApplication
        app = QApplication(sys.argv)
        
        from main_window import MainWindow
        
        # 创建主窗口
        window = MainWindow()
        
        print("✓ 主窗口创建成功")
        
        # 检查基础检查数据
        basic_check_data = window.apps_data.get("基础检查", [])
        print(f"基础检查项目数量: {len(basic_check_data)}")
        
        for item in basic_check_data:
            print(f"  - {item.get('name')}")
        
        # 手动调用 _display_category_grid 方法
        print("\n手动调用 _display_category_grid...")
        
        # 创建一个测试布局
        from PyQt5.QtWidgets import QVBoxLayout, QWidget
        test_widget = QWidget()
        test_layout = QVBoxLayout(test_widget)
        
        # 调用方法
        window._display_category_grid(basic_check_data, test_layout, "基础检查")
        
        print("✓ _display_category_grid 调用成功")
        
        # 检查是否创建了表格
        from PyQt5.QtWidgets import QTableWidget
        tables = test_widget.findChildren(QTableWidget)
        print(f"创建的表格数量: {len(tables)}")
        
        for table in tables:
            print(f"  表格对象名: {table.objectName()}")
            print(f"  表格行数: {table.rowCount()}")
            print(f"  表格列数: {table.columnCount()}")
        
        # 测试 _add_basic_check_empty_table 方法
        print("\n手动调用 _add_basic_check_empty_table...")
        
        test_widget2 = QWidget()
        test_layout2 = QVBoxLayout(test_widget2)
        
        window._add_basic_check_empty_table(test_layout2)
        
        print("✓ _add_basic_check_empty_table 调用成功")
        
        tables2 = test_widget2.findChildren(QTableWidget)
        print(f"创建的表格数量: {len(tables2)}")
        
        for table in tables2:
            print(f"  表格对象名: {table.objectName()}")
            print(f"  表格行数: {table.rowCount()}")
            print(f"  表格列数: {table.columnCount()}")
            
            # 检查表格内容
            for row in range(table.rowCount()):
                row_content = []
                for col in range(table.columnCount()):
                    item = table.item(row, col)
                    if item:
                        row_content.append(item.text())
                    else:
                        row_content.append("空")
                print(f"    第{row+1}行: {row_content}")
        
        app.quit()
        return True
        
    except Exception as e:
        print(f"调试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("开始调试基础检查功能...\n")
    
    success = debug_basic_check()
    
    if success:
        print("\n✓ 调试完成!")
    else:
        print("\n✗ 调试失败!")

if __name__ == "__main__":
    main()
