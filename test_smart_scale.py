#!/usr/bin/env python3
"""
测试智能缩放功能
"""

import sys
sys.path.append('.')

def test_smart_scale_calculation():
    """测试智能缩放计算"""
    from flow import FlowWidget
    
    # 创建一个FlowWidget实例来测试方法
    widget = FlowWidget()
    
    # 测试场景1: 容器比图片大，需要放大
    print("=== 测试场景1: 小图片放大到填充大容器 ===")
    container_w, container_h = 1144, 464  # 您提到的容器尺寸
    image_w, image_h = 300, 200  # 小图片
    
    new_w, new_h, scale = widget._calculate_smart_scale_size(
        image_w, image_h, container_w, container_h
    )
    
    print(f"原始图片: {image_w}x{image_h}")
    print(f"容器尺寸: {container_w}x{container_h}")
    print(f"缩放后: {new_w}x{new_h}")
    print(f"缩放比例: {scale:.2f}")
    print(f"填充效果: {'宽度填满' if new_w >= container_w else '高度填满'}")
    
    # 测试场景2: 容器比图片小，需要缩小
    print("\n=== 测试场景2: 大图片缩小到适应容器 ===")
    image_w, image_h = 2000, 1500  # 大图片
    
    new_w, new_h, scale = widget._calculate_smart_scale_size(
        image_w, image_h, container_w, container_h
    )
    
    print(f"原始图片: {image_w}x{image_h}")
    print(f"容器尺寸: {container_w}x{container_h}")
    print(f"缩放后: {new_w}x{new_h}")
    print(f"缩放比例: {scale:.2f}")
    print(f"填充效果: {'宽度填满' if new_w >= container_w else '高度填满'}")
    
    # 测试场景3: 您提到的实际图片尺寸
    print("\n=== 测试场景3: 实际图片 914x232 ===")
    image_w, image_h = 914, 232  # 您提到的图片尺寸
    
    new_w, new_h, scale = widget._calculate_smart_scale_size(
        image_w, image_h, container_w, container_h
    )
    
    print(f"原始图片: {image_w}x{image_h}")
    print(f"容器尺寸: {container_w}x{container_h}")
    print(f"缩放后: {new_w}x{new_h}")
    print(f"缩放比例: {scale:.2f}")
    print(f"填充效果: {'宽度填满' if new_w >= container_w else '高度填满'}")
    
    # 验证比例保持
    original_ratio = image_w / image_h
    new_ratio = new_w / new_h
    print(f"原始宽高比: {original_ratio:.3f}")
    print(f"缩放后宽高比: {new_ratio:.3f}")
    print(f"比例保持: {'✓' if abs(original_ratio - new_ratio) < 0.001 else '✗'}")
    
    return True

def main():
    """主测试函数"""
    print("智能缩放功能测试")
    print("=" * 60)
    
    try:
        test_smart_scale_calculation()
        print("\n" + "=" * 60)
        print("✓ 所有测试通过！")
        print("\n智能缩放特点:")
        print("• 优先填充整个容器（像手机相册缩放）")
        print("• 保持图片原始比例不变形")
        print("• 支持放大小图片（最大3倍）")
        print("• 支持缩小大图片（最小0.1倍）")
        print("• 同时支持静态图片和GIF动画")
        return 0
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
