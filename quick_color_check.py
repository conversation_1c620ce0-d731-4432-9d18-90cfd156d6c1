#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速颜色检查 - 禁用CSS后的效果
"""

import sys
import os

# 添加当前目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def quick_color_check():
    """快速颜色检查"""
    print("=== 快速颜色检查 (禁用CSS后) ===")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtTest import QTest
        from PyQt5.QtCore import Qt
        
        app = QApplication(sys.argv)
        
        from main_window import MainWindow
        
        # 创建主窗口
        window = MainWindow()
        window.show()
        
        print("✓ 主窗口创建成功")
        
        # 等待界面完全加载
        QTest.qWait(1500)
        app.processEvents()
        
        print(f"分类列表项目数量: {window.category_list.count()}")
        
        # 检查每个分类的颜色
        print("\n=== 检查分类颜色 (禁用CSS后) ===")
        
        color_groups = {
            "蓝色系": ["基础检查", "装配", "调试", "测试", "标定"],
            "绿色系": ["便捷工具", "信息查询", "流程生成助手", "状态监控系统", "故障诊断系统"],
            "橙色系": ["社区", "日志管理", "主题设置", "首页"]
        }
        
        for group_name, categories in color_groups.items():
            print(f"\n{group_name}:")
            for category in categories:
                # 查找对应的分类项
                for i in range(window.category_list.count()):
                    item = window.category_list.item(i)
                    if item.text() == category:
                        # 获取背景颜色
                        background = item.background()
                        color = background.color()
                        r, g, b, a = color.red(), color.green(), color.blue(), color.alpha()
                        
                        if a > 0:
                            if group_name == "蓝色系" and b > 200:
                                print(f"  ✅ {category}: RGB({r}, {g}, {b}) - 蓝色正确")
                            elif group_name == "绿色系" and g > 150:
                                print(f"  ✅ {category}: RGB({r}, {g}, {b}) - 绿色正确")
                            elif group_name == "橙色系" and r > 200:
                                print(f"  ✅ {category}: RGB({r}, {g}, {b}) - 橙色正确")
                            else:
                                print(f"  ⚠️ {category}: RGB({r}, {g}, {b}) - 颜色不符合预期")
                        else:
                            print(f"  ❌ {category}: 无颜色 (alpha={a})")
                        break
        
        print(f"\n🎯 现在您应该能看到侧边栏有明显的颜色区分了！")
        print("🔵 蓝色区域: 核心检测功能")
        print("🟢 绿色区域: 扩展工具功能")
        print("🟠 橙色区域: 管理设置功能")
        
        # 关闭应用
        window.close()
        app.quit()
        
        return True
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    quick_color_check()
