#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
全局列数规则演示脚本
演示恢复窗口=4列，最大化窗口=6列的全局规则
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QWidget, QVBoxLayout, QHBoxLayout, QPushButton, QLabel, QTextEdit
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QFont

# 添加主程序路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from main_window import MainWindow
except ImportError as e:
    print(f"导入错误: {e}")
    sys.exit(1)

class ColumnRuleDemoWindow(QWidget):
    """列数规则演示窗口"""
    
    def __init__(self):
        super().__init__()
        self.main_window = None
        self.init_ui()
        
    def init_ui(self):
        """初始化UI"""
        self.setWindowTitle("全局列数规则演示")
        self.setGeometry(50, 50, 600, 400)
        
        layout = QVBoxLayout()
        
        # 标题
        title = QLabel("全局列数规则演示")
        title.setFont(QFont("Microsoft YaHei", 16, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("color: #2E86AB; margin: 10px;")
        layout.addWidget(title)
        
        # 规则说明
        rules_text = """
📋 全局列数规则：

✅ 恢复窗口状态：所有卡片网格使用 4 列显示
✅ 最大化窗口状态：所有卡片网格使用 6 列显示

🔧 实现特点：
• 移除所有硬编码的列数设置
• 使用自适应管理器统一管理列数
• 所有布局组件自动适应窗口状态
• 智能卡片容器支持动态列数调整
        """
        
        rules_label = QLabel(rules_text)
        rules_label.setFont(QFont("Microsoft YaHei", 11))
        rules_label.setStyleSheet("""
            QLabel {
                background-color: #F8F9FA;
                border: 1px solid #DEE2E6;
                border-radius: 8px;
                padding: 15px;
                margin: 10px;
            }
        """)
        layout.addWidget(rules_label)
        
        # 控制按钮
        button_layout = QHBoxLayout()
        
        self.launch_btn = QPushButton("🚀 启动主程序")
        self.launch_btn.clicked.connect(self.launch_main_window)
        self.launch_btn.setFont(QFont("Microsoft YaHei", 12))
        self.launch_btn.setStyleSheet("""
            QPushButton {
                background-color: #28A745;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 10px 20px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #218838;
            }
            QPushButton:pressed {
                background-color: #1E7E34;
            }
        """)
        button_layout.addWidget(self.launch_btn)
        
        self.toggle_btn = QPushButton("🔄 切换窗口状态")
        self.toggle_btn.clicked.connect(self.toggle_window_state)
        self.toggle_btn.setEnabled(False)
        self.toggle_btn.setFont(QFont("Microsoft YaHei", 12))
        self.toggle_btn.setStyleSheet("""
            QPushButton {
                background-color: #007BFF;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 10px 20px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #0056B3;
            }
            QPushButton:pressed {
                background-color: #004085;
            }
            QPushButton:disabled {
                background-color: #6C757D;
            }
        """)
        button_layout.addWidget(self.toggle_btn)
        
        layout.addLayout(button_layout)
        
        # 状态显示
        self.status_label = QLabel("状态：等待启动主程序...")
        self.status_label.setFont(QFont("Microsoft YaHei", 11))
        self.status_label.setAlignment(Qt.AlignCenter)
        self.status_label.setStyleSheet("""
            QLabel {
                background-color: #E9ECEF;
                border-radius: 4px;
                padding: 8px;
                margin: 10px;
            }
        """)
        layout.addWidget(self.status_label)
        
        # 说明文本
        instruction_text = """
💡 使用说明：

1. 点击"启动主程序"按钮启动工具箱应用
2. 主程序启动后，点击"切换窗口状态"按钮
3. 观察应用卡片的列数变化：
   • 恢复窗口：4列显示
   • 最大化窗口：6列显示
4. 可以多次切换来验证规则的一致性

⚠️ 注意：请确保主程序完全加载后再进行切换操作
        """
        
        instruction_label = QLabel(instruction_text)
        instruction_label.setFont(QFont("Microsoft YaHei", 10))
        instruction_label.setStyleSheet("""
            QLabel {
                background-color: #FFF3CD;
                border: 1px solid #FFEAA7;
                border-radius: 6px;
                padding: 12px;
                margin: 10px;
            }
        """)
        layout.addWidget(instruction_label)
        
        self.setLayout(layout)
        
        # 设置窗口样式
        self.setStyleSheet("""
            QWidget {
                background-color: white;
            }
        """)
        
    def launch_main_window(self):
        """启动主窗口"""
        try:
            self.status_label.setText("状态：正在启动主程序...")
            self.status_label.setStyleSheet("""
                QLabel {
                    background-color: #FFF3CD;
                    border-radius: 4px;
                    padding: 8px;
                    margin: 10px;
                }
            """)
            
            self.main_window = MainWindow()
            self.main_window.show()
            
            # 等待窗口完全加载
            QTimer.singleShot(3000, self.enable_toggle_button)
            
            self.launch_btn.setEnabled(False)
            
        except Exception as e:
            self.status_label.setText(f"状态：启动失败 - {e}")
            self.status_label.setStyleSheet("""
                QLabel {
                    background-color: #F8D7DA;
                    border-radius: 4px;
                    padding: 8px;
                    margin: 10px;
                    color: #721C24;
                }
            """)
            
    def enable_toggle_button(self):
        """启用切换按钮"""
        self.toggle_btn.setEnabled(True)
        self.status_label.setText("状态：主程序已就绪，可以切换窗口状态")
        self.status_label.setStyleSheet("""
            QLabel {
                background-color: #D4EDDA;
                border-radius: 4px;
                padding: 8px;
                margin: 10px;
                color: #155724;
            }
        """)
        
        # 显示当前状态
        self.update_status_display()
        
    def toggle_window_state(self):
        """切换窗口状态"""
        if not self.main_window:
            return
            
        try:
            # 使用主窗口的切换方法
            self.main_window.toggle_window_size()
            
            # 等待状态更新后显示
            QTimer.singleShot(500, self.update_status_display)
            
        except Exception as e:
            self.status_label.setText(f"状态：切换失败 - {e}")
            
    def update_status_display(self):
        """更新状态显示"""
        if not self.main_window:
            return
            
        try:
            is_maximized = self.main_window.isMaximized()
            window_state = "最大化" if is_maximized else "恢复窗口"
            expected_cols = 6 if is_maximized else 4
            actual_cols = self.main_window.adaptive_manager.get_max_cols()
            
            status_text = f"状态：{window_state} | 列数：{actual_cols} | 规则：{'✅' if actual_cols == expected_cols else '❌'}"
            
            self.status_label.setText(status_text)
            
        except Exception as e:
            self.status_label.setText(f"状态：更新失败 - {e}")

def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 设置应用样式
    app.setStyle('Fusion')
    
    # 创建演示窗口
    demo_window = ColumnRuleDemoWindow()
    demo_window.show()
    
    sys.exit(app.exec_())

if __name__ == '__main__':
    main()
