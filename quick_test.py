#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速测试启动界面
"""

import sys
import os

# 添加当前目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def quick_test():
    """快速测试"""
    print("=== 快速测试启动界面 ===")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtTest import QTest
        from PyQt5.QtCore import Qt, QTimer
        
        app = QApplication(sys.argv)
        
        from main_window import MainWindow
        
        # 创建主窗口
        window = MainWindow()
        window.show()
        
        print("✓ 主窗口创建成功")
        print(f"✓ 窗口标题: {window.windowTitle()}")
        print(f"✓ 标题栏高度: {window.title_bar.height()}px")
        
        # 等待界面加载
        QTest.qWait(1000)
        app.processEvents()

        # 检查是否自动显示了基础检查界面
        print("等待自动显示基础检查界面...")
        QTest.qWait(1000)  # 等待main.py中的QTimer触发
        app.processEvents()
        
        # 检查当前选择的分类
        current_item = window.category_list.currentItem()
        if current_item:
            print(f"✓ 当前选择的分类: {current_item.text()}")
        else:
            print("✗ 没有选择任何分类")
        
        # 关闭应用
        window.close()
        app.quit()
        
        return True
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    quick_test()
